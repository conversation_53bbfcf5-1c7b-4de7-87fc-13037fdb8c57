const { createApp, ref, computed, onMounted, nextTick } = Vue;

createApp({
    setup() {
        // 用户信息
        const currentUser = ref('');
        const token = ref('');
        
        // 侧边栏状态
        const isSidebarCollapsed = ref(false);
        
        // 对话状态
        const conversations = ref([]);
        const currentConversationId = ref(null);
        const messages = ref([]);

        // 计算属性：分组对话
        const groupedConversations = computed(() => {
            console.log('Computing groupedConversations, conversations count:', conversations.value.length);

            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            const groups = [
                { title: '置顶对话', conversations: [] },
                { title: '今天', conversations: [] },
                { title: '最近一周', conversations: [] },
                { title: '最近30天', conversations: [] },
                { title: '更早', conversations: [] }
            ];

            conversations.value.forEach(conversation => {
                console.log(`Processing conversation ${conversation.id}: sticky=${conversation.sticky_flag}, updated=${conversation.updated_at}`);
                const updatedAt = new Date(conversation.updated_at);

                // 置顶对话优先
                if (conversation.sticky_flag) {
                    groups[0].conversations.push(conversation);
                } else if (updatedAt >= today) {
                    groups[1].conversations.push(conversation);
                } else if (updatedAt >= weekAgo) {
                    groups[2].conversations.push(conversation);
                } else if (updatedAt >= monthAgo) {
                    groups[3].conversations.push(conversation);
                } else {
                    groups[4].conversations.push(conversation);
                }
            });

            // 只返回有对话的分组
            const result = groups.filter(group => group.conversations.length > 0);
            console.log('Grouped conversations result:', result);
            return result;
        });
        
        // 菜单状态
        const openConversationMenuId = ref(null);
        
        // 消息状态
        const newMessage = ref('');
        const isSending = ref(false);
        const isReceiving = ref(false);
        
        // DOM引用
        const messagesContainer = ref(null);
        const messageInput = ref(null);
        
        // 格式化时间
        const formatTime = (timestamp) => {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        };
        
        // 切换侧边栏
        const toggleSidebar = () => {
            isSidebarCollapsed.value = !isSidebarCollapsed.value;
        };
        
        // 切换对话菜单
        const toggleConversationMenu = (conversationId) => {
            if (openConversationMenuId.value === conversationId) {
                openConversationMenuId.value = null;
            } else {
                openConversationMenuId.value = conversationId;
            }
        };
        
        // 点击其他地方关闭菜单
        const handleClickOutside = (event) => {
            if (openConversationMenuId.value) {
                // 检查点击的元素是否在菜单内部
                const menuElements = document.querySelectorAll('.conversation-menu');
                let isClickInsideMenu = false;
                
                menuElements.forEach(element => {
                    if (element.contains(event.target)) {
                        isClickInsideMenu = true;
                    }
                });
                
                // 如果点击不在菜单内部，则关闭菜单
                if (!isClickInsideMenu) {
                    openConversationMenuId.value = null;
                }
            }
        };
        
        // 置顶对话
        const pinConversation = async (conversationId) => {
            console.log('pinConversation called with ID:', conversationId);
            openConversationMenuId.value = null;

            try {
                // 找到要置顶的对话
                const conversation = conversations.value.find(c => c.id === conversationId);
                if (!conversation) {
                    console.error('Conversation not found:', conversationId);
                    alert('对话不存在');
                    return;
                }

                console.log('Current conversation:', conversation);

                // 切换置顶状态
                const newStickyFlag = !conversation.sticky_flag;
                console.log('Toggling sticky flag to:', newStickyFlag);

                const response = await fetch(`/api/conversations/${conversationId}/sticky`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        sticky_flag: newStickyFlag
                    })
                });

                console.log('API response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('API response data:', result);

                    // 显示成功消息
                    console.log(`置顶状态已${newStickyFlag ? '开启' : '关闭'}`);

                    // 重新加载对话列表以获取最新状态
                    await loadConversations();
                    console.log('Conversations reloaded');
                } else {
                    const errorText = await response.text();
                    console.error('Failed to update sticky status:', response.status, errorText);
                    alert('更新置顶状态失败');
                }
            } catch (error) {
                console.error('Pin conversation error:', error);
                alert('操作失败：' + error.message);
            }
        };
        
        // 处理登出
        const handleLogout = async () => {
            try {
                // 调用后端登出接口
                await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                // 无论后端调用是否成功，都清除本地存储
                localStorage.removeItem('token');
                localStorage.removeItem('currentUser');
                window.location.href = '/static/login.html';
            }
        };
        
        // 加载对话列表
        const loadConversations = async () => {
            try {
                console.log('Loading conversations...');
                const response = await fetch('/api/conversations', {
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });

                console.log('Conversations response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('Raw API response:', result);

                    const data = result.data || result; // 处理标准响应格式
                    console.log('Processed data:', data);

                    conversations.value = data.map(conv => ({
                        id: conv.id,
                        title: conv.title || `对话 ${conv.id}`,
                        preview: '暂无消息',
                        created_at: conv.created_at,
                        updated_at: conv.updated_at || conv.created_at,
                        sticky_flag: conv.sticky_flag || false
                    }));

                    console.log('Updated conversations.value:', conversations.value);

                    // 如果有对话，默认选择第一个
                    if (data.length > 0 && !currentConversationId.value) {
                        currentConversationId.value = data[0].id;
                        await loadMessages(data[0].id);
                    }
                } else {
                    console.error('Failed to load conversations:', response.status);
                }
            } catch (error) {
                console.error('Load conversations error:', error);
            }
        };
        
        // 创建新对话
        const createNewConversation = async () => {
            try {
                const response = await fetch('/api/conversations/new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        title: `对话 ${new Date().toLocaleDateString('zh-CN')} ${new Date().toLocaleTimeString('zh-CN')}`
                    })
                });
                
                if (response.ok) {
                    const newConversation = await response.json();
                    conversations.value.unshift({
                        id: newConversation.id,
                        title: newConversation.title || `对话 ${newConversation.id}`,
                        preview: '暂无消息',
                        created_at: newConversation.created_at
                    });
                    
                    selectConversation(newConversation.id);
                }
            } catch (error) {
                console.error('Create conversation error:', error);
            }
        };
        
        // 选择对话
        const selectConversation = async (conversationId) => {
            if (currentConversationId.value === conversationId) return;
            
            currentConversationId.value = conversationId;
            messages.value = [];
            await loadMessages(conversationId);
        };
        
        // 删除对话
        const deleteConversation = async (conversationId) => {
            if (conversations.value.length <= 1) {
                alert('至少保留一个对话');
                openConversationMenuId.value = null;
                return;
            }
            
            if (!confirm('确定要删除这个对话吗？')) {
                openConversationMenuId.value = null;
                return;
            }
            
            try {
                const response = await fetch(`/api/conversations/${conversationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
                
                if (response.ok) {
                    // 从列表中移除
                    const index = conversations.value.findIndex(c => c.id === conversationId);
                    if (index !== -1) {
                        conversations.value.splice(index, 1);
                    }
                    
                    // 如果删除的是当前对话，切换到第一个对话
                    if (currentConversationId.value === conversationId) {
                        if (conversations.value.length > 0) {
                            selectConversation(conversations.value[0].id);
                        } else {
                            currentConversationId.value = null;
                            messages.value = [];
                        }
                    }
                }
            } catch (error) {
                console.error('Delete conversation error:', error);
            } finally {
                openConversationMenuId.value = null;
            }
        };
        
        // 加载消息
        const loadMessages = async (conversationId) => {
            try {
                const response = await fetch(`/api/messages?conversation_id=${conversationId}`, {
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    // 根据标准响应格式处理数据
                    if (result.code === 200) {
                        // 后端返回的数据格式是 {conversation_id: [messages]}
                        messages.value = result.data[conversationId] || [];
                    } else {
                        console.error('Failed to load messages:', result.message);
                    }
                    scrollToBottom();
                }
            } catch (error) {
                console.error('Load messages error:', error);
            }
        };
        
        // 发送消息
        const sendMessage = async () => {
            const message = newMessage.value.trim();
            if (!message || isSending.value) return;
            
            newMessage.value = '';
            
            // 添加用户消息到界面
            const userMessage = {
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            };
            messages.value.push(userMessage);
            
            scrollToBottom();
            
            isSending.value = true;
            isReceiving.value = true;
            
            try {
                // 添加AI消息占位符
                const aiMessageIndex = messages.value.length;
                messages.value.push({
                    role: 'assistant',
                    content: '',
                    timestamp: new Date().toISOString()
                });
                
                // 发起流式请求
                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        conversation_id: currentConversationId.value,
                        message: message,
                        collection_name: 'FinancialResearchOffice',
                        input: {}
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let assistantResponse = '';
                let buffer = ''; // 用于累积不完整的数据

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        break;
                    }

                    // 解码数据并添加到缓冲区
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // 按行分割数据，保留最后一个可能不完整的行
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || ''; // 保留最后一个可能不完整的行

                    for (const line of lines) {
                        const trimmedLine = line.trim();
                        if (trimmedLine.startsWith('data: ')) {
                            const data = trimmedLine.substring(6).trim();

                            if (data === '[DONE]') {
                                // 流结束
                                isReceiving.value = false;
                                break;
                            }

                            try {
                                const parsedData = JSON.parse(data);
                                console.log('Received data:', parsedData); // 调试日志

                                // 检查业务code
                                if (parsedData.code === 200 && parsedData.data?.content) {
                                    // 更新AI回复内容
                                    assistantResponse += parsedData.data.content;
                                    messages.value[aiMessageIndex].content = assistantResponse;
                                    scrollToBottom();
                                } else if (parsedData.code !== 200 && parsedData.data?.error) {
                                    // 处理错误
                                    messages.value[aiMessageIndex].content = `错误: ${parsedData.data.error}`;
                                    isReceiving.value = false;
                                    break;
                                }
                            } catch (e) {
                                console.error('解析数据失败:', e, 'Raw data:', data);
                            }
                        }
                    }

                    if (!isReceiving.value) {
                        break;
                    }
                }

                // 处理缓冲区中剩余的数据
                if (buffer.trim()) {
                    const trimmedLine = buffer.trim();
                    if (trimmedLine.startsWith('data: ')) {
                        const data = trimmedLine.substring(6).trim();
                        if (data !== '[DONE]') {
                            try {
                                const parsedData = JSON.parse(data);
                                if (parsedData.code === 200 && parsedData.data?.content) {
                                    assistantResponse += parsedData.data.content;
                                    messages.value[aiMessageIndex].content = assistantResponse;
                                    scrollToBottom();
                                }
                            } catch (e) {
                                console.error('解析缓冲区数据失败:', e, 'Raw data:', data);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Send message error:', error);
                messages.value.push({
                    role: 'assistant',
                    content: `错误: ${error.message}`,
                    timestamp: new Date().toISOString()
                });
                isReceiving.value = false;
            } finally {
                isSending.value = false;
                scrollToBottom();
            }
        };
        
        // 处理键盘事件
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        };
        
        // 滚动到底部
        const scrollToBottom = () => {
            nextTick(() => {
                if (messagesContainer.value) {
                    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
                }
            });
        };
        
        // 检查认证状态
        const checkAuth = () => {
            const storedToken = localStorage.getItem('token');
            const storedUser = localStorage.getItem('currentUser');
            
            if (!storedToken || !storedUser) {
                window.location.href = '/static/login.html';
                return false;
            }
            
            token.value = storedToken;
            currentUser.value = storedUser;
            return true;
        };
        
        // 页面加载时检查认证状态并加载数据
        onMounted(() => {
            if (checkAuth()) {
                loadConversations();
                document.addEventListener('click', handleClickOutside);
            }
        });
        
        return {
            // 状态
            currentUser,
            isSidebarCollapsed,
            conversations,
            groupedConversations,
            currentConversationId,
            messages,
            newMessage,
            isSending,
            isReceiving,
            openConversationMenuId,

            // DOM引用
            messagesContainer,
            messageInput,

            // 方法
            formatTime,
            toggleSidebar,
            toggleConversationMenu,
            pinConversation,
            handleLogout,
            createNewConversation,
            selectConversation,
            deleteConversation,
            sendMessage,
            handleKeyDown
        };
    }
}).mount('#app');