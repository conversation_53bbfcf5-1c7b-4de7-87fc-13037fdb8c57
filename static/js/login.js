const { createApp, ref } = Vue;

createApp({
    setup() {
        const isLoggingIn = ref(false);
        const loginForm = ref({
            username: '',
            password: ''
        });
        const loginError = ref('');
        
        // 处理登录
        const handleLogin = async () => {
            if (!loginForm.value.username || !loginForm.value.password) {
                loginError.value = '请输入用户名和密码';
                return;
            }
            
            isLoggingIn.value = true;
            loginError.value = '';
            
            try {
                // 模拟登录请求
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: loginForm.value.username,
                        password: loginForm.value.password
                    })
                });
                
                const data = await response.json();
                
                // 检查业务code而不是HTTP状态码
                if (data.code === 200) {
                    // 直接使用localStorage而不是未定义的响应式变量
                    localStorage.setItem('token', data.data.access_token);
                    localStorage.setItem('currentUser', data.data.user.username);
                    window.location.href = '/static/chat.html';
                } else {
                    loginError.value = data.message || '登录失败';
                }
            } catch (error) {
                loginError.value = '网络错误，请稍后重试';
                console.error('Login error:', error);
            } finally {
                isLoggingIn.value = false;
            }
        };

        return {
            isLoggingIn,
            loginForm,
            loginError,
            handleLogin
        };
    }
}).mount('#app');